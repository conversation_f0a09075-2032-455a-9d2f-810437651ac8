#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬取投放资质页面的game_publish_id
"""

import requests
import json
import time
import os
from typing import List, Dict, Any


class QualificationCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://ad.qq.com/sso_cgi/customer_service_cgi/v2/certification_package/list"

        # 审核状态映射
        self.approval_status_map = {
            0: "审核中",
            1: "审核通过",
            2: "审核驳回"
        }
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json; charset=UTF-8',
            'origin': 'https://ad.qq.com',
            'priority': 'u=1, i',
            'referer': 'https://ad.qq.com/cm/manager/qualification/list',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-account-id': '********',
            'x-trace-id': '0aceeeae-e530-5418-565f-f040b0bd3e87',
            'x-trans-id': '6d49c42f-7474-94c7-0b0f-3fb7d2a7f312'
        }
        
        # 设置cookies
        self.cookies = {
            'gdt_token': 'TGT-23512-Z_rbh2v.0ptcSwWiTL4VcrT57ggaDTB0nOlWP51V4TFN9Q_VeoYwSEOYxFGsn1gG',
            'gdt_protect': 'e0dd762993bf2c68216cc0f238bb20b683a4051b',
            'SSO_SESSION_ID': '302a5f9d-6c97-11f0-b03b-9fc957a51192',
            'atlas_platform': 'atlas',
            'BM_SSO_UID_LIST': '********'
        }
        
        # 请求参数
        self.params = {
            'g_tk': '**********',
            'trace_id': 'e13c7e6a-809b-91ef-0303-3f570fc3af5e',
            'g_trans_id': '6d49c42f-7474-94c7-0b0f-3fb7d2a7f312'
        }

    def get_certification_list(self, page_num: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取认证包列表
        
        Args:
            page_num: 页码
            page_size: 每页大小
            
        Returns:
            响应数据
        """
        data = {
            "mdm_id": 58851560,
            "industry_id": "",
            "approval_status": "",
            "creator_operator": "",
            "expire_time": "",
            "package_name": "",
            "page": {
                "page_num": page_num,
                "page_size": page_size
            },
            "source_platform": ""
        }
        
        try:
            response = self.session.post(
                self.base_url,
                params=self.params,
                headers=self.headers,
                cookies=self.cookies,
                json=data,
                timeout=30
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return {}

    def extract_qualification_data(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从响应数据中提取game_publish_id和approval_status

        Args:
            response_data: API响应数据

        Returns:
            包含game_publish_id和approval_status的字典列表
        """
        qualification_data = []

        try:
            if response_data.get('code') == 0 and 'data' in response_data:
                data = response_data['data']
                if 'list' in data:
                    for item in data['list']:
                        current_package = item.get('current_package', {})
                        ext_info = current_package.get('ext_info', {})
                        game_publish_id = ext_info.get('game_publish_id')
                        approval_status = current_package.get('approval_status')
                        package_name = current_package.get('certification_package_name', '')

                        if game_publish_id:
                            status_text = self.approval_status_map.get(approval_status, f"未知状态({approval_status})")

                            qualification_info = {
                                'game_publish_id': game_publish_id,
                                'approval_status': approval_status,
                                'approval_status_text': status_text,
                                'package_name': package_name
                            }

                            qualification_data.append(qualification_info)
                            print(f"找到资质: {game_publish_id} | 状态: {status_text} | 名称: {package_name}")

        except Exception as e:
            print(f"提取资质数据时出错: {e}")

        return qualification_data

    def crawl_all_pages(self) -> List[Dict[str, Any]]:
        """
        爬取所有页面的资质数据

        Returns:
            所有资质数据列表
        """
        all_qualification_data = []
        page_num = 1
        page_size = 20

        print("开始爬取投放资质数据...")

        while True:
            print(f"正在爬取第 {page_num} 页...")

            response_data = self.get_certification_list(page_num, page_size)

            if not response_data or response_data.get('code') != 0:
                print(f"第 {page_num} 页请求失败或返回错误")
                break

            # 提取当前页的资质数据
            page_qualification_data = self.extract_qualification_data(response_data)
            all_qualification_data.extend(page_qualification_data)

            # 检查是否还有下一页
            data = response_data.get('data', {})
            page_info = data.get('page_info', {})
            total_page = page_info.get('total_page', 0)

            print(f"当前页: {page_num}/{total_page}, 本页找到 {len(page_qualification_data)} 个资质")

            if page_num >= total_page:
                break

            page_num += 1
            time.sleep(0.5)  # 避免请求过快

        print(f"爬取完成，总共找到 {len(all_qualification_data)} 个资质")
        return all_qualification_data

    def save_to_file(self, qualification_data: List[Dict[str, Any]],
                     appid_filename: str = "投放资质appid.txt",
                     detail_filename: str = "投放资质详情.txt"):
        """
        保存资质数据到文件

        Args:
            qualification_data: 资质数据列表
            appid_filename: 保存appid的文件名
            detail_filename: 保存详情的文件名
        """
        try:
            # 提取唯一的game_publish_id
            game_publish_ids = [item['game_publish_id'] for item in qualification_data]
            unique_ids = list(set(game_publish_ids))

            # 保存appid列表
            with open(appid_filename, 'w', encoding='utf-8') as f:
                for game_id in unique_ids:
                    f.write(f"{game_id}\n")
            print(f"已保存 {len(unique_ids)} 个唯一的game_publish_id到文件: {appid_filename}")

            # 保存详细信息
            with open(detail_filename, 'w', encoding='utf-8') as f:
                f.write("AppID\t审核状态\t状态描述\t资质名称\n")
                for item in qualification_data:
                    f.write(f"{item['game_publish_id']}\t{item['approval_status']}\t{item['approval_status_text']}\t{item['package_name']}\n")
            print(f"已保存 {len(qualification_data)} 条详细资质信息到文件: {detail_filename}")

            # 统计各状态数量
            status_count = {}
            for item in qualification_data:
                status = item['approval_status_text']
                status_count[status] = status_count.get(status, 0) + 1

            print("\n审核状态统计:")
            for status, count in status_count.items():
                print(f"  {status}: {count} 个")

        except Exception as e:
            print(f"保存文件时出错: {e}")


def main():
    """主函数"""
    crawler = QualificationCrawler()

    # 爬取所有页面的资质数据
    qualification_data = crawler.crawl_all_pages()

    if qualification_data:
        # 保存到文件
        crawler.save_to_file(qualification_data, "投放资质appid.txt", "投放资质详情.txt")
    else:
        print("未找到任何资质数据")


if __name__ == "__main__":
    main()
